<template>
  <div class="user-page page">
    <div class="user-header">
      <div class="user-info">
        <div class="avatar">
          <div class="avatar-content">
            <van-icon name="contact" />
          </div>
        </div>
        <div class="info">
          <h3 class="name">{{ userInfo.name }}</h3>
          <p class="id">{{ $t('user.loginName') }}: {{ userInfo.loginName }}</p>
        </div>
      </div>
    </div>

    <div class="user-content">
      <van-cell-group class="menu-group" title="个人中心">
        <van-cell :title="$t('user.profile')" is-link icon="contact" @click="showProfilePopup" />
        <van-cell :title="$t('user.changePassword')" is-link icon="lock" @click="showPasswordPopup" />
        <van-cell :title="$t('user.myFavorites')" is-link icon="star-o" to="/favor" />
        <van-cell :title="$t('user.myWarranty')" is-link icon="shield-o" to="/warranty" />
        <van-cell :title="$t('user.myCart')" is-link icon="cart-o" to="/cart" />
      </van-cell-group>

      <van-cell-group class="menu-group">
        <van-cell :title="$t('user.language')" is-link icon="chat-o">
          <template #right-icon>
            <language-switcher />
          </template>
        </van-cell>
        <van-cell title="语言切换测试" is-link icon="setting-o" to="/test/language" />
      </van-cell-group>

      <div class="logout-button">
        <van-button block color="#f39c12" @click="logout">{{
          $t("common.logout")
        }}</van-button>
      </div>
    </div>

    <!-- 个人资料弹窗 -->
    <van-popup v-model:show="showProfile" position="bottom" round closeable close-icon="close"
      :style="{ height: '90%' }">
      <div class="popup-title">{{ $t('user.profile') }}</div>
      <div class="popup-content">
        <van-form @submit="submitProfile" :label-align="$i18n.locale === 'zh-CN' ? 'left' : 'top'">
          <van-cell-group inset>
            <van-field v-model="profileForm.dealerName" name="dealerName" :label="$t('user.dealerName')"
              :rules="[{ required: true, message: $t('validation.required') }]" required />
            <van-field v-model="profileForm.employerIdentificationNumber" name="employerIdentificationNumber"
              :label="$t('user.ein')" />
            <van-field v-model="profileForm.doingBusinessAs" name="doingBusinessAs" :label="$t('user.dba')" />
            <van-field v-model="profileForm.website" name="website" :label="$t('user.website')" />
            <van-field v-model="profileForm.street" name="street" :label="$t('user.street')"
              :rules="[{ required: true, message: $t('validation.required') }]" required />
            <van-field v-model="profileForm.city" name="city" :label="$t('user.city')"
              :rules="[{ required: true, message: $t('validation.required') }]" required />
            <van-field v-model="profileForm.state" name="state" :label="$t('user.state')"
              :rules="[{ required: true, message: $t('validation.required') }]" required />
            <van-field v-model="profileForm.postalCode" name="postalCode" :label="$t('user.postalCode')"
              :rules="[{ required: true, message: $t('validation.required') }]" required />
            <van-field v-model="profileForm.contactName" name="contactName" :label="$t('user.contactName')"
              :rules="[{ required: true, message: $t('validation.required') }]" required />
            <van-field v-model="profileForm.contactTittle" name="contactTittle" :label="$t('user.contactTitle')"
              :rules="[{ required: true, message: $t('validation.required') }]" required />
            <van-field v-model="profileForm.phone" name="phone" :label="$t('user.phone')"
              :rules="[{ required: true, message: $t('validation.required') }]" required />
            <van-field v-model="profileForm.email" name="email" :label="$t('user.email')" type="email"
              :rules="[{ required: true, message: $t('validation.required') }]" required />
            <van-field v-model="profileForm.laborShopRate" name="laborShopRate" :label="$t('user.laborShopRate')"
              :rules="[{ required: true, message: $t('validation.required') }]" required />
          </van-cell-group>
          <div style="margin: 16px;">
            <van-button round block type="primary" native-type="submit" :loading="loading">
              {{ $t('common.submit') }}
            </van-button>
          </div>
        </van-form>
      </div>
    </van-popup>

    <!-- 修改密码弹窗 -->
    <van-popup v-model:show="showPassword" position="bottom" round closeable close-icon="close"
      :style="{ height: '40%' }">
      <div class="popup-title">{{ $t('user.changePassword') }}</div>
      <div class="popup-content">
        <van-form @submit="submitPassword" :label-align="$i18n.locale === 'zh-CN' ? 'left' : 'top'">
          <van-cell-group inset>
            <van-field v-model="passwordForm.newPassword" type="password" name="newPassword"
              :label="$t('user.newPassword')"
              :rules="[{ required: true, message: $t('validation.required') }, { min: 6, message: $t('validation.passwordLength') }]"
              required />
            <van-field v-model="passwordForm.confirmPassword" type="password" name="confirmPassword"
              :label="$t('user.confirmPassword')" :rules="[
                { required: true, message: $t('validation.required') },
                { validator: validatePasswordConfirm, message: $t('validation.passwordMatch') }
              ]" required />
          </van-cell-group>
          <div style="margin: 16px;">
            <van-button round block type="primary" native-type="submit" :loading="passwordLoading">
              {{ $t('common.submit') }}
            </van-button>
          </div>
        </van-form>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useI18n } from "vue-i18n";
import { showToast, showConfirmDialog } from "vant";
import { useUserStore } from "@/stores/user.js";
import apiService from "@/utils/api";
import LanguageSwitcher from "@/components/LanguageSwitcher.vue";

const router = useRouter();
const { t } = useI18n();
const userStore = useUserStore();

// 用户信息
const userInfo = computed(() => {
  return userStore.userInfo || {
    name: t('user.guest'),
    id: '--',
    loginName: '--'
  };
});



// 个人资料弹窗控制
const showProfile = ref(false);
const loading = ref(false);
const profileForm = reactive({
  dealerName: '',
  employerIdentificationNumber: '',
  doingBusinessAs: '',
  website: '',
  street: '',
  city: '',
  state: '',
  postalCode: '',
  contactName: '',
  contactTittle: '',
  phone: '',
  email: '',
  laborShopRate: ''
});

// 修改密码弹窗控制
const showPassword = ref(false);
const passwordLoading = ref(false);
const passwordForm = reactive({
  newPassword: '',
  confirmPassword: ''
});

// 密码确认验证
const validatePasswordConfirm = (val) => {
  return val === passwordForm.newPassword;
};

// 获取用户个人资料数据
const fetchUserProfile = async () => {
  if (!userStore.isLoggedIn()) return;

  loading.value = true;
  try {
    // 直接调用API获取个人资料数据
    const response = await apiService.user.getUserProfile(userStore.userInfo);
    if (response) {
      // 将获取到的数据填充到表单中
      Object.assign(profileForm, response);
      // 同时更新到store中
      userStore.userProfile = response;
    }
  } catch (error) {
    console.error('获取个人资料失败:', error);
  } finally {
    loading.value = false;
  }
};

// 显示个人资料弹窗
const showProfilePopup = async () => {
  // 如果还没有获取过个人资料，则先获取
  if (!userStore.userProfile) {
    await fetchUserProfile();
  } else {
    // 如果已经获取过，直接使用store中的数据
    Object.assign(profileForm, userStore.userProfile);
  }
  showProfile.value = true;
};

// 显示修改密码弹窗
const showPasswordPopup = () => {
  passwordForm.newPassword = '';
  passwordForm.confirmPassword = '';
  showPassword.value = true;
};

// 提交个人资料
const submitProfile = async () => {
  loading.value = true;
  try {
    const response = await apiService.user.updateUserProfile(profileForm);
    if (response) {
      // 更新store中的数据
      userStore.userProfile = { ...profileForm };

      showToast({
        message: t('user.profileUpdateSuccess'),
      });
      showProfile.value = false;
    }
  } catch (error) {
    showToast({
      message: t('common.error'),
    });
  } finally {
    loading.value = false;
  }
};

// 提交密码修改
const submitPassword = async () => {
  passwordLoading.value = true;
  try {
    await apiService.user.modifyPassword({
      newPassword: passwordForm.newPassword,
      confirmPassword: passwordForm.confirmPassword
    });
    showToast({
      message: t('user.passwordUpdateSuccess'),
    });
    showPassword.value = false;
  } catch (error) {
    showToast({
      message: t('common.error'),
    });
  } finally {
    passwordLoading.value = false;
  }
};

// 退出登录
const logout = () => {
  showConfirmDialog({
    title: t("user.logoutTitle"),
    message: t("user.logoutConfirm"),
    confirmButtonText: t("common.confirm"),
    cancelButtonText: t("common.cancel"),
  })
    .then(() => {
      // 调用登出API
      apiService.user.logout()
        .then(() => {
          // 无论API是否成功，都清除用户信息
          userStore.clearUserInfo();


          // 跳转到登录页
          router.replace("/login");
        })
        .catch(error => {
          console.error('退出登录失败，但仍将继续登出:', error);
          // 即使API调用失败，也清除用户信息并跳转登录页
          userStore.clearUserInfo();
          router.replace("/login");
        });
    })
    .catch(() => {
      // 取消退出
    });
};

// 页面加载时获取个人资料数据
onMounted(() => {
  fetchUserProfile();
});
</script>

<style lang="scss" scoped>
.user-page {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 20px;
  display: flex;
  flex-direction: column;

  .user-header {
    background: linear-gradient(135deg, #e74c3c, #f39c12);
    padding: 30px 16px;
    padding: 20px 20px 50px;
    margin-bottom: 20px;
    color: white;
    border-bottom-left-radius: 30px;
    border-bottom-right-radius: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: -10%;
      right: -10%;
      width: 200px;
      height: 200px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      z-index: 0;
    }

    &::after {
      content: "";
      position: absolute;
      bottom: -5%;
      left: -5%;
      width: 120px;
      height: 120px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.08);
      z-index: 0;
    }

    .user-info {
      display: flex;
      align-items: center;
      position: relative;
      z-index: 1;

      .avatar {
        margin-right: 20px;
        width: 80px;
        height: 80px;
        cursor: pointer;
        transition: transform 0.3s ease;

        &:active {
          transform: scale(0.95);
        }

        .avatar-content {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.9);
          box-shadow: 0 3px 8px rgba(0, 0, 0, 0.12);
          border: 1px solid rgba(231, 76, 60, 0.15);
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          .van-icon {
            font-size: 50px;
            color: #e74c3c;
            opacity: 0.85;
            transition: opacity 0.2s ease;
          }
        }

        &:hover .avatar-content {
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.16);
          border-color: rgba(231, 76, 60, 0.25);

          .van-icon {
            opacity: 1;
          }
        }
      }

      .info {
        .name {
          margin: 0 0 8px;
          font-size: 22px;
          font-weight: 600;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .id {
          margin: 0;
          font-size: 14px;
          opacity: 0.85;
          background: rgba(0, 0, 0, 0.15);
          padding: 4px 12px;
          border-radius: 20px;
          display: inline-block;
        }
      }
    }
  }

  .user-content {
    padding: 10px 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-top: -20px;

    .menu-group {
      margin-bottom: 12px;

      :deep(.van-cell) {
        &:after {
          border-bottom-color: rgba(231, 76, 60, 0.1);
        }

        .van-icon {
          color: #e74c3c;
        }
      }
    }

    .logout-button {
      margin-top: auto;
      padding: 0 16px;
      margin-bottom: 50px;

      :deep(.van-button) {
        border-radius: 20px;
      }
    }
  }
}

.popup-title {
  text-align: center;
  padding: 16px;
  font-size: 18px;
  font-weight: bold;
  border-bottom: 1px solid #ebedf0;
}

.popup-content {
  padding: 16px 0;
  overflow-y: auto;
  max-height: calc(90vh - 60px);
}

/* 响应式设计 - 极简头像适配 */
@media (max-width: 375px) {
  .user-page {
    .user-header {
      padding: 20px 12px 40px;

      .user-info {
        .avatar {
          width: 70px;
          height: 70px;
          margin-right: 16px;

          .avatar-content {
            .van-icon {
              font-size: 45px;
            }
          }
        }

        .info {
          .name {
            font-size: 20px;
          }

          .id {
            font-size: 13px;
            padding: 3px 10px;
          }
        }
      }
    }
  }
}

@media (orientation: landscape) {
  .user-page {
    .user-header {
      padding: 16px 20px 32px;

      .user-info {
        .avatar {
          width: 60px;
          height: 60px;
          margin-right: 16px;

          .avatar-content {
            .van-icon {
              font-size: 35px;
            }
          }
        }

        .info {
          .name {
            font-size: 18px;
            margin-bottom: 6px;
          }

          .id {
            font-size: 12px;
            padding: 3px 8px;
          }
        }
      }
    }
  }
}

@media (orientation: landscape) and (min-width: 567px) {
  .user-page {
    .user-header {
      padding: 12px 20px 28px;

      .user-info {
        .avatar {
          width: 50px;
          height: 50px;
          margin-right: 14px;

          .avatar-content {
            .van-icon {
              font-size: 30px;
            }
          }
        }

        .info {
          .name {
            font-size: 16px;
            margin-bottom: 4px;
          }

          .id {
            font-size: 11px;
            padding: 2px 8px;
          }
        }
      }
    }
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .user-page {
    .user-header {
      padding: 24px 24px 48px;

      .user-info {
        .avatar {
          width: 90px;
          height: 90px;
          margin-right: 24px;

          .avatar-content {
            .van-icon {
              font-size: 50px;
            }
          }
        }

        .info {
          .name {
            font-size: 24px;
            margin-bottom: 10px;
          }

          .id {
            font-size: 15px;
            padding: 5px 14px;
          }
        }
      }
    }
  }
}
</style>