import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { resolve } from "path";
import postcssPxToViewport from "postcss-px-to-viewport";

// https://vite.dev/config/
export default defineConfig({
  server: {
    host: "0.0.0.0",
    port: 5173,
    proxy: {
      "/sparts-wb": {
        //target: "http://localhost:8080",
        target: "https://order.weibang.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/sparts-wb/, "/sparts-wb"),
      },
      "/fileviewer": {
        target: "https://order.weibang.com",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/fileviewer/, "/fileviewer"),
      },
    },
    // 配置 history 模式的回退
    historyApiFallback: true,
  },
  plugins: [vue()],
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
  // 构建配置
  build: {
    // 确保在生产环境中正确处理 history 模式
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          vant: ['vant']
        }
      }
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `
          @use "@/assets/styles/variables.scss" as *;
          @use "@/assets/styles/mixins.scss" as *;
        `,
      },
    },
    postcss: {
      plugins: [
        postcssPxToViewport({
          unitToConvert: "px",
          viewportWidth: 375, // 视窗的宽度，对应设计稿的宽度
          viewportHeight: 667, // 视窗的高度，根据设备的比例来设定
          unitPrecision: 5, // 指定`px`转换为视窗单位值的小数位数
          propList: ["*"], // 能转化为vw的属性列表
          viewportUnit: "vw", // 指定需要转换成的视窗单位，建议使用vw
          fontViewportUnit: "vw", // 字体使用的视窗单位
          selectorBlackList: [".ignore", ".hairlines"], // 指定不转换为视窗单位的类
          minPixelValue: 1, // 小于或等于`1px`不转换为视窗单位
          mediaQuery: false, // 允许在媒体查询中转换`px`
          exclude: [/node_modules\/vant/i], // 排除vant组件库
          landscape: false, // 是否处理横屏情况
        }),
      ],
    },
  },
});
