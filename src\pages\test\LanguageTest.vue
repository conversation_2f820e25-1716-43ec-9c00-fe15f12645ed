<template>
  <div class="language-test">
    <van-nav-bar title="语言切换测试" left-arrow @click-left="$router.back()" />
    
    <div class="test-content">
      <van-cell-group>
        <van-cell title="当前语言" :value="currentLocale" />
        <van-cell title="localStorage中的语言" :value="storedLocale" />
        <van-cell title="环境变量语言" :value="envLanguage" />
        <van-cell title="配置文件语言" :value="configLanguage" />
      </van-cell-group>
      
      <div class="language-switcher-test">
        <h3>语言切换组件测试</h3>
        <LanguageSwitcher />
      </div>
      
      <div class="translation-test">
        <h3>翻译测试</h3>
        <van-cell-group>
          <van-cell title="应用名称" :value="$t('common.appName')" />
          <van-cell title="首页" :value="$t('common.home')" />
          <van-cell title="目录" :value="$t('common.catalog')" />
          <van-cell title="订单" :value="$t('common.order')" />
          <van-cell title="我的" :value="$t('common.user')" />
          <van-cell title="语言设置" :value="$t('user.language')" />
        </van-cell-group>
      </div>
      
      <div class="manual-switch">
        <h3>手动切换测试</h3>
        <van-button-group>
          <van-button @click="switchTo('zh-CN')" :type="currentLocale === 'zh-CN' ? 'primary' : 'default'">
            中文
          </van-button>
          <van-button @click="switchTo('en')" :type="currentLocale === 'en' ? 'primary' : 'default'">
            English
          </van-button>
        </van-button-group>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import LanguageSwitcher from '@/components/LanguageSwitcher.vue'

const { locale, t } = useI18n()

// 响应式数据
const storedLocale = ref('')
const envLanguage = ref('')
const configLanguage = ref('')

// 计算属性
const currentLocale = computed(() => locale.value)

// 手动切换语言
const switchTo = (lang) => {
  locale.value = lang
  localStorage.setItem('locale', lang)
  updateInfo()
}

// 更新信息
const updateInfo = () => {
  storedLocale.value = localStorage.getItem('locale') || '未设置'
  envLanguage.value = import.meta.env?.VITE_LANGUAGE || '未设置'
  configLanguage.value = '从配置文件读取'
}

// 组件挂载时更新信息
onMounted(() => {
  updateInfo()
})
</script>

<style lang="scss" scoped>
.language-test {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.test-content {
  padding: 16px;
  
  h3 {
    margin: 20px 0 10px 0;
    font-size: 16px;
    font-weight: 600;
    color: #323233;
  }
  
  .language-switcher-test {
    background: white;
    padding: 16px;
    border-radius: 8px;
    margin: 16px 0;
    
    .language-switcher {
      display: flex;
      justify-content: center;
      padding: 10px;
      border: 1px solid #ebedf0;
      border-radius: 4px;
    }
  }
  
  .translation-test {
    background: white;
    padding: 16px;
    border-radius: 8px;
    margin: 16px 0;
  }
  
  .manual-switch {
    background: white;
    padding: 16px;
    border-radius: 8px;
    margin: 16px 0;
    text-align: center;
    
    .van-button-group {
      margin-top: 10px;
    }
  }
}
</style>
