{"name": "erpsparts-wb-website-phone", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "npm run dev:cn", "dev:cn": "vite --mode cn", "dev:en": "vite --mode en", "build": "vite build", "build:cn": "vite build --mode cn", "build:en": "vite build --mode en", "preview": "vite preview"}, "dependencies": {"axios": "^1.10.0", "dayjs": "^1.11.13", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.3.0", "sass": "^1.89.2", "unplugin-vue-components": "^28.7.0", "vant": "^4.9.20", "vue": "^3.5.13", "vue-i18n": "^9.9.0", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "postcss-px-to-viewport": "^1.1.1", "vite": "^6.3.5"}, "description": "Vue3+Vite移动端应用，支持横竖屏适配", "main": "vite.config.js", "keywords": ["vue3", "vite", "mobile", "landscape", "portrait"], "author": "", "license": "ISC"}